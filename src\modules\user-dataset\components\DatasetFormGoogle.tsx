import React, { useState } from 'react';
import { Card, Input, Button, FormItem, Textarea, IconCard } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';
import ChatLayoutGoogle from './ChatLayoutGoogle';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';

// Form data interface for Google dataset
interface GoogleDatasetFormData {
  name: string;
  description: string;
}

interface DatasetFormGoogleProps {
  /**
   * Callback when dataset creation is successful
   */
  onSuccess?: () => void;

  /**
   * Callback when conversations change
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component form for creating Google dataset with ConversationSidebarGoogle
 * Interface: ChatLayoutGoogle + ConversationSidebarGoogle + ChatPanel + DatasetForm
 * No JSON file import
 */
const DatasetFormGoogle: React.FC<DatasetFormGoogleProps> = ({
  onSuccess,
  onConversationsChange,
}) => {
  const { t } = useTranslation();
  const { createAndUpload, isLoading } = useCreateAndUploadDataset();

  // State for conversations and dataset
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Form state
  const [formData, setFormData] = useState<GoogleDatasetFormData>({
    name: '',
    description: '',
  });
  const [errors, setErrors] = useState<Partial<GoogleDatasetFormData>>({});

  // Handle conversations change from ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    // Notify parent component
    if (onConversationsChange) {
      onConversationsChange(updatedConversations);
    }
  };

  // Validate form data
  const validateForm = (): boolean => {
    const newErrors: Partial<GoogleDatasetFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('Dataset name is required');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('Dataset description is required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (field: keyof GoogleDatasetFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      if (conversations.length === 0) {
        console.error('No conversations available');
        return;
      }

      // Convert conversations to JSONL format
      const trainJsonlData = convertConversationsToJsonl(conversations);

      // Validate JSONL data
      const validation = validateJsonlData(trainJsonlData);
      if (!validation.isValid) {
        console.error('Training data validation failed:', validation.errors);
        return;
      }

      // Create dataset info for Google
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: formData.name,
        description: formData.description,
        provider: ProviderFineTuneEnum.GOOGLE,
        trainDataset: 'application/jsonl',
        // Google does not need validation data
      };

      // Create and upload dataset
      await createAndUpload({
        datasetInfo,
        trainJsonlData,
      });

      // Reset form and notify success
      setFormData({ name: '', description: '' });
      setErrors({});
      setConversations([]);
      setShowForm(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating Google dataset:', error);
    }
  };

  return (
    <div>
      {/* Form Header */}
      {showForm && (
        <Card>
          <form>
            <div className="grid grid-cols-1 gap-4 mb-4">
              <FormItem
                name="name"
                label={t('user-dataset:createDataset.formGoogle.name', 'Name')}
                helpText={errors.name}
                required
              >
                <Input
                  value={formData.name}
                  onChange={e => handleInputChange('name', e.target.value)}
                  placeholder={t(
                    'user-dataset:createDataset.formGoogle.placeholderName',
                    'Enter dataset name for Google...'
                  )}
                  error={errors.name || ''}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('user-dataset:createDataset.formGoogle.description', 'Description')}
                helpText={errors.description}
                required
              >
                <Textarea
                  value={formData.description}
                  onChange={e => handleInputChange('description', e.target.value)}
                  placeholder={t(
                    'user-dataset:createDataset.formGoogle.placeholderName',
                    'Enter dataset description for Google...'
                  )}
                  status={errors.description ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>

            <div className="flex justify-end items-center">
              <div className="flex space-x-2 ">
                <IconCard
                  icon="close"
                  variant="secondary"
                  size="md"
                  title={t('user-dataset:createDataset.action.cancel')}
                  onClick={() => setShowForm(false)}
                />
                <IconCard
                  icon="check"
                  variant="primary"
                  size="md"
                  title={t('user-dataset:createDataset.action.createDataset')}
                  onClick={handleSubmit}
                  disabled={conversations.length === 0}
                  isLoading={isLoading}
                />
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Chat Layout Container */}
      <div className="relative">
        <div className="h-[600px] ">
          <ChatLayoutGoogle onConversationsChange={handleConversationsChange} />
        </div>

        {/* Create Dataset Button */}
        {conversations.length > 0 && !showForm && (
          <div className="absolute top-4 right-4 ">
            <Button onClick={() => setShowForm(true)} size="sm">
              {t('user-dataset:createDataset.action.createDataset')} ({conversations.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatasetFormGoogle;
