{"admin-dataset": {"title": "数据集和模型管理", "description": "为用户管理数据集和AI模型", "dataFineTune": {"title": "数据集微调", "description": "管理用于训练和微调AI模型的数据集。"}, "createDataset": {"notification": {"success": {"title": "成功", "message": "OpenAI数据集已成功创建！", "messageGoogle": "Google数据集已成功创建！"}, "error": {"title": "错误", "message": "无法创建数据集。请重试。"}}}, "menu": {"overview": "概览", "dataFineTune": "数据集微调", "createOpenAI": "创建OpenAI数据集", "createGoogle": "创建Google数据集"}, "createFineTuneModel": {"title": "创建微调模型", "form": {"modelName": "模型名称", "description": "描述", "dataset": "数据集", "baseModel": "基础模型", "suffix": "后缀", "modelSource": "基础模型来源", "userKeyLLM": "选择用户密钥LLM", "hyperparameters": "超参数配置", "epochs": "训练轮数", "batchSize": "批次大小", "learningRate": "学习率"}, "placeholders": {"modelName": "输入模型名称", "description": "输入模型描述", "suffix": "输入模型后缀", "selectDataset": "-- 选择微调数据集 --", "selectSystemModel": "-- 选择系统模型 --", "selectUserModel": "-- 选择用户模型 --", "selectUserKey": "-- 选择用户密钥 --", "selectKeyFirst": "请先选择LLM密钥"}, "buttons": {"systemModels": "系统模型", "userModels": "用户模型（密钥）", "auto": "自动", "custom": "自定义", "cancel": "取消", "create": "创建模型"}, "loading": {"datasets": "正在加载数据集...", "systemModels": "正在加载系统模型...", "userKeys": "正在加载密钥列表...", "userModels": "正在从密钥加载模型...", "userKeysLoading": "正在加载用户密钥列表..."}, "messages": {"noDatasets": "没有可用的数据集", "noUserKeys": "没有可用的用户密钥", "noUserKeysAvailable": "没有可用的用户密钥LLM", "noModelsAvailable": "没有可用的模型", "noModelsForKey": "此密钥没有可用的模型", "selectKeyFirst": "选择用户密钥LLM以查看可用模型", "loadingModelsFromKey": "正在从所选密钥加载模型...", "modelsFound": "从此密钥找到{{count}}个模型", "loadingError": "加载模型时出错：{{error}}"}}, "apiIntegration": {"title": "API集成", "tabs": {"systemModels": "系统模型", "userModels": "用户模型", "fineTuneModels": "微调模型"}, "loading": {"loadingData": "正在加载数据..."}, "messages": {"noUserKeys": "没有可用的用户密钥", "noModelsForKey": "所选密钥没有可用的模型", "noSystemModels": "没有可用的系统模型"}, "form": {"selectUserKey": "选择用户密钥以查看模型"}, "placeholders": {"selectUserKey": "-- 选择用户密钥 --"}}, "providerSelection": {"createDatasetUsingOpenAI": "使用OpenAI创建数据集", "createDatasetUsingGoogle": "使用Google创建数据集"}, "chatLayout": {"training": {"title": "训练数据聊天", "description": "导入JSONL文件或为训练数据创建新对话", "defaultConversationTitle": "训练对话", "defaultChatTitle": "训练聊天", "placeholder": "输入训练数据消息...", "messages": {"importSuccess": "✅ 成功从{{type}}文件导入{{count}}个对话", "importError": "⚠️ 没有导入有效的对话。请检查文件格式。", "parseError": "解析文件时出错：{{error}}。请检查文件格式。"}}, "validation": {"title": "验证数据聊天", "description": "导入JSONL文件或为验证数据创建新对话", "defaultConversationTitle": "验证对话", "defaultChatTitle": "验证聊天", "placeholder": "输入验证数据消息..."}}, "conversationSidebar": {"title": "对话", "buttons": {"closeSidebar": "关闭侧边栏", "openSidebar": "打开侧边栏", "importJsonl": "导入JSONL", "newChat": "新建聊天", "deleteConversation": "删除对话"}, "messages": {"noConversations": "还没有对话", "importToStart": "导入JSONL文件开始"}}, "chatPanelWithRoleLogic": {"startConversation": "开始对话", "startConversationDescription": "添加消息以创建训练数据集"}, "datasetCard": {"deleteSuccess": {"title": "成功", "message": "数据集已成功删除！"}, "deleteError": {"title": "错误", "message": "无法删除数据集。请重试。"}, "deleteConfirm": {"title": "确认删除", "message": "您确定要删除此数据集吗？此操作无法撤销。"}, "statusUpdateSuccess": {"title": "成功", "message": "数据集状态已成功更新！"}, "statusUpdateError": {"title": "错误", "message": "无法更新数据集状态。请重试。"}}}}